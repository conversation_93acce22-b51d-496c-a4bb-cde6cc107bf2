<?php
/*
Plugin Name: Auto Default Tags
Plugin URI: https://yourwebsite.com
Description: Automatically adds 3 tags from different categories for better distribution. Covers 16 categories: Agriculture, Breaking News, Business, Education, Entertainment, Health, Judiciary, Politics, Religion, Security, Police, Sports, Technology, World Stage, Lifestyle, and Economy. Over 300 tags available. Tags are added only once per post to prevent duplicates.
Version: 2.2
Author: Your Name
Author URI: https://yourwebsite.com
*/

if (!defined('ABSPATH')) exit; // Exit if accessed directly

function adt_add_default_tags_to_post($post_ID) {
    // Only run on posts
    if (get_post_type($post_ID) !== 'post') return;

    // Check if we've already added tags to this post
    $tags_already_added = get_post_meta($post_ID, '_adt_tags_added', true);
    if ($tags_already_added) return;

    // Organized tag categories for better distribution
    $tag_categories = array(
        'Agriculture' => array(
            "Nigeria Agriculture","Farming in Nigeria","Food Security Nigeria","Cassava Farming Nigeria","Rice Farming Nigeria",
            "Agro Processing Nigeria","Anchor <PERSON>ers Programme","Agriculture News Nigeria","Livestock Farming Nigeria","Fisheries Nigeria",
            "Agriculture Policy Nigeria","Farm Inputs Nigeria","Rural Development Nigeria","Climate Smart Agriculture","Agritech Nigeria"
        ),
        'Breaking News' => array(
            "Breaking News Nigeria","Nigeria Breaking News Today","Lagos Breaking News","Abuja Breaking News","Emergency News Nigeria",
            "Nigeria Protest Today","Crime News Nigeria","ASUU Strike Update","Accident News Nigeria","Nigeria Crisis Update",
            "Security Breach Nigeria","Just In Nigeria News","Developing Story Nigeria","Major Incident Nigeria","Viral News Nigeria",
            "Latest Updates Nigeria","Top Stories Nigeria"
        ),
        'Business' => array(
            "Nigeria Business News","Nigerian Economy","CBN News Today","Naira Exchange Rate","Nigerian Stock Market","NSE Nigeria",
            "Inflation Nigeria","Dangote News Today","BUA Group Nigeria","MTN Nigeria News","Entrepreneurship in Nigeria",
            "SME Business Nigeria","Banking Sector Nigeria","Fintech Nigeria","Telecoms Nigeria","Oil and Gas Nigeria",
            "Nigerian Startups","Lagos Business News"
        ),
        'Education' => array(
            "Education News Nigeria","ASUU Strike Update 2025","JAMB Result 2025","WAEC Result 2025","NECO Result 2025",
            "NUC Nigeria","Nigerian Universities News","Student Loan Nigeria","Scholarships in Nigeria","Lecturers Strike Nigeria",
            "Tertiary Education Nigeria","University Admission Nigeria","Education Policy Nigeria","Alumni News Nigeria","Private Universities Nigeria",
            "Nigerian Education News","Universities in Nigeria"
        ),
        'Entertainment' => array(
            "Nigeria Entertainment News","Nollywood Movies 2025","Afrobeats Music Nigeria","Burna Boy News","Wizkid Latest Song",
            "Davido News Today","Tiwa Savage Latest News","BNXN News Nigeria","Celebrity Gist Nigeria","Nigerian Music Awards",
            "AMVCA 2025","Headies 2025","Comedy Skits Nigeria","Fashion Trends Nigeria","Social Media Trends Nigeria",
            "Nigerian Celebrities","Nigerian Lifestyle","Lagos Events","Nigerian Culture"
        ),
        'Health' => array(
            "Health News Nigeria","NCDC Nigeria","Disease Outbreak Nigeria","Lassa Fever Update","Maternal Health Nigeria",
            "Mental Health in Nigeria","Nutrition Nigeria","Fitness Nigeria","NHIS Nigeria","Medical Research Nigeria",
            "WHO Nigeria Updates","Vaccination News Nigeria","Medical Tourism Nigeria","Traditional Medicine Nigeria","COVID-19 Nigeria Update",
            "Nigerian Hospitals","Nigerian Healthcare Updates"
        ),
        'Judiciary' => array(
            "Nigeria Judiciary News","Supreme Court Nigeria","Court of Appeal Nigeria","EFCC Court Case","ICPC Nigeria News",
            "Corruption Trial Nigeria","Human Rights Nigeria","Judicial Reform Nigeria","Electoral Tribunal Nigeria","Constitutional Matter Nigeria",
            "Legal Practice in Nigeria","SAN Nigeria","NBA Nigeria","High Court Nigeria","Arraignment News Nigeria"
        ),
        'Politics' => array(
            "Nigeria Politics News","APC News Today","PDP News Today","Labour Party Nigeria","INEC Nigeria Updates",
            "Tinubu News Today","Atiku Abubakar News","Peter Obi News","Nigerian Senate News","House of Reps Nigeria",
            "State Assembly Nigeria","APC Primary Election","PDP Crisis Nigeria","Nigeria Campaign 2025","Governance in Nigeria",
            "Lagos News","Abuja News","Government News Nigeria","Political Updates Nigeria","Local Elections Nigeria",
            "Nigeria News","Nigerian News","Nigeria Daily News","Nigeria Newspapers","Nigerian Newspaper Today",
            "Online News from Nigeria","Nigeria News Today"
        ),
        'Religion' => array(
            "Religion News Nigeria","Christianity in Nigeria","Islam in Nigeria","CAN Nigeria","PFN Nigeria",
            "NSCIA Nigeria","Church News Nigeria","Mosque News Nigeria","Pastor News Nigeria","Imam News Nigeria",
            "Crusade Nigeria","Prayer in Nigeria","Pilgrimage Nigeria","Eid Celebration Nigeria","Christmas Celebration Nigeria"
        ),
        'Security' => array(
            "Security News Nigeria","Terrorism in Nigeria","Boko Haram News","Bandits in Nigeria","Kidnapping in Nigeria",
            "Nigerian Army News","Police News Nigeria","Insurgency in Nigeria","Counter Terrorism Nigeria","Cybercrime Nigeria",
            "EFCC News Today","Piracy in Nigeria","Oil Theft Nigeria","Security Summit Nigeria","Community Policing Nigeria"
        ),
        'Police' => array(
            "Nigeria Police News","NPF Nigeria","Police Brutality Nigeria","SARS Nigeria","Arrest News Nigeria",
            "IGP Nigeria","Police Reform Nigeria","Crime Prevention Nigeria","Criminal Investigation Nigeria","Police Recruitment Nigeria",
            "Police Promotion Nigeria","Detention News Nigeria","Tactical Squad Nigeria","Police PRO Nigeria","Interpol Nigeria"
        ),
        'Sports' => array(
            "Nigeria Sports News","Nigeria Football News","Super Eagles News","NPFL Nigeria","Nigerian Sports Updates","Lagos Football",
            "Nigerian Athletes","Nigerian Premier League","AFCON 2025 Qualifiers","World Cup Qualifiers Nigeria","Victor Osimhen News",
            "Victor Boniface News","Anthony Joshua Fight","Nigerian Basketball News","D'Tigers Nigeria","NFF Nigeria",
            "Transfer News Nigeria","Champions League Nigeria","Europa League Nigeria"
        ),
        'Technology' => array(
            "Nigerian Tech News","Nigeria Startups","Fintech Nigeria","Tech Startups Nigeria","Mobile Apps Nigeria","AI in Nigeria",
            "ICT in Nigeria","Broadband Internet Nigeria","Data Plans Nigeria","Tech Innovation Nigeria","Startup Funding Nigeria",
            "Tech Hubs Nigeria","E-commerce Nigeria","Cybersecurity Nigeria","Digital Economy Nigeria"
        ),
        'World Stage' => array(
            "World News Today","International News Nigeria","US News Today","UK News Today","China News Nigeria",
            "Russia News Today","ECOWAS News Nigeria","AU News Nigeria","UN News Nigeria","Niger Coup Update",
            "Ghana News Nigeria","South Africa News Today","Global Economy News","Climate Change News","Nigerians in Diaspora"
        ),
        'Lifestyle' => array(
            "Nigeria Lifestyle News","Fashion Nigeria","Food and Recipes Nigeria","Beauty Trends Nigeria","Travel Nigeria",
            "Tourism in Nigeria","Nigerian Weddings","Family Life Nigeria","Relationship Tips Nigeria","Culture Nigeria",
            "Lagos Lifestyle","Abuja Lifestyle","Youth Culture Nigeria","Nigerian Traditions","Lifestyle Bloggers Nigeria"
        ),
        'Economy' => array(
            "Nigeria Economy News","Inflation in Nigeria","Exchange Rate Nigeria","CBN Monetary Policy","Nigerian Debt News",
            "National Budget Nigeria","Economic Growth Nigeria","Unemployment in Nigeria","Poverty in Nigeria","Nigeria GDP",
            "Federal Revenue Nigeria","Fiscal Policy Nigeria","Global Oil Prices Nigeria","Nigeria Economic Summit","Development Plan Nigeria"
        )
    );

    // Pick 3 tags from different categories for better distribution
    $category_names = array_keys($tag_categories);
    $selected_categories = array_rand($category_names, min(3, count($category_names)));

    // Ensure we have an array even if only one category is selected
    if (!is_array($selected_categories)) {
        $selected_categories = array($selected_categories);
    }

    $tags_to_add = array();
    foreach ($selected_categories as $category_index) {
        $category_name = $category_names[$category_index];
        $category_tags = $tag_categories[$category_name];

        // Pick one random tag from this category
        $random_tag = $category_tags[array_rand($category_tags)];
        $tags_to_add[] = $random_tag;
    }

    // If we need more tags (shouldn't happen with 16 categories), fill from random categories
    while (count($tags_to_add) < 3 && count($tags_to_add) < count($category_names)) {
        $remaining_categories = array_diff($category_names, array_keys(array_flip($tags_to_add)));
        if (empty($remaining_categories)) break;

        $random_category = $remaining_categories[array_rand($remaining_categories)];
        $random_tag = $tag_categories[$random_category][array_rand($tag_categories[$random_category])];
        $tags_to_add[] = $random_tag;
    }

    wp_set_post_tags($post_ID, $tags_to_add, true);

    // Mark that we've added tags to this post
    update_post_meta($post_ID, '_adt_tags_added', true);
}

// Function to add tags to existing posts (used by admin interface)
function adt_add_tags_to_existing_post($post_ID) {
    // Only run on posts
    if (get_post_type($post_ID) !== 'post') return false;

    // Check if we've already added tags to this post
    $tags_already_added = get_post_meta($post_ID, '_adt_tags_added', true);
    if ($tags_already_added) return false;

    // Use the same organized tag categories as the main function
    $tag_categories = array(
        'Agriculture' => array(
            "Nigeria Agriculture","Farming in Nigeria","Food Security Nigeria","Cassava Farming Nigeria","Rice Farming Nigeria",
            "Agro Processing Nigeria","Anchor Borrowers Programme","Agriculture News Nigeria","Livestock Farming Nigeria","Fisheries Nigeria",
            "Agriculture Policy Nigeria","Farm Inputs Nigeria","Rural Development Nigeria","Climate Smart Agriculture","Agritech Nigeria"
        ),
        'Breaking News' => array(
            "Breaking News Nigeria","Nigeria Breaking News Today","Lagos Breaking News","Abuja Breaking News","Emergency News Nigeria",
            "Nigeria Protest Today","Crime News Nigeria","ASUU Strike Update","Accident News Nigeria","Nigeria Crisis Update",
            "Security Breach Nigeria","Just In Nigeria News","Developing Story Nigeria","Major Incident Nigeria","Viral News Nigeria",
            "Latest Updates Nigeria","Top Stories Nigeria"
        ),
        'Business' => array(
            "Nigeria Business News","Nigerian Economy","CBN News Today","Naira Exchange Rate","Nigerian Stock Market","NSE Nigeria",
            "Inflation Nigeria","Dangote News Today","BUA Group Nigeria","MTN Nigeria News","Entrepreneurship in Nigeria",
            "SME Business Nigeria","Banking Sector Nigeria","Fintech Nigeria","Telecoms Nigeria","Oil and Gas Nigeria",
            "Nigerian Startups","Lagos Business News"
        ),
        'Education' => array(
            "Education News Nigeria","ASUU Strike Update 2025","JAMB Result 2025","WAEC Result 2025","NECO Result 2025",
            "NUC Nigeria","Nigerian Universities News","Student Loan Nigeria","Scholarships in Nigeria","Lecturers Strike Nigeria",
            "Tertiary Education Nigeria","University Admission Nigeria","Education Policy Nigeria","Alumni News Nigeria","Private Universities Nigeria",
            "Nigerian Education News","Universities in Nigeria"
        ),
        'Entertainment' => array(
            "Nigeria Entertainment News","Nollywood Movies 2025","Afrobeats Music Nigeria","Burna Boy News","Wizkid Latest Song",
            "Davido News Today","Tiwa Savage Latest News","BNXN News Nigeria","Celebrity Gist Nigeria","Nigerian Music Awards",
            "AMVCA 2025","Headies 2025","Comedy Skits Nigeria","Fashion Trends Nigeria","Social Media Trends Nigeria",
            "Nigerian Celebrities","Nigerian Lifestyle","Lagos Events","Nigerian Culture"
        ),
        'Health' => array(
            "Health News Nigeria","NCDC Nigeria","Disease Outbreak Nigeria","Lassa Fever Update","Maternal Health Nigeria",
            "Mental Health in Nigeria","Nutrition Nigeria","Fitness Nigeria","NHIS Nigeria","Medical Research Nigeria",
            "WHO Nigeria Updates","Vaccination News Nigeria","Medical Tourism Nigeria","Traditional Medicine Nigeria","COVID-19 Nigeria Update",
            "Nigerian Hospitals","Nigerian Healthcare Updates"
        ),
        'Judiciary' => array(
            "Nigeria Judiciary News","Supreme Court Nigeria","Court of Appeal Nigeria","EFCC Court Case","ICPC Nigeria News",
            "Corruption Trial Nigeria","Human Rights Nigeria","Judicial Reform Nigeria","Electoral Tribunal Nigeria","Constitutional Matter Nigeria",
            "Legal Practice in Nigeria","SAN Nigeria","NBA Nigeria","High Court Nigeria","Arraignment News Nigeria"
        ),
        'Politics' => array(
            "Nigeria Politics News","APC News Today","PDP News Today","Labour Party Nigeria","INEC Nigeria Updates",
            "Tinubu News Today","Atiku Abubakar News","Peter Obi News","Nigerian Senate News","House of Reps Nigeria",
            "State Assembly Nigeria","APC Primary Election","PDP Crisis Nigeria","Nigeria Campaign 2025","Governance in Nigeria",
            "Lagos News","Abuja News","Government News Nigeria","Political Updates Nigeria","Local Elections Nigeria",
            "Nigeria News","Nigerian News","Nigeria Daily News","Nigeria Newspapers","Nigerian Newspaper Today",
            "Online News from Nigeria","Nigeria News Today"
        ),
        'Religion' => array(
            "Religion News Nigeria","Christianity in Nigeria","Islam in Nigeria","CAN Nigeria","PFN Nigeria",
            "NSCIA Nigeria","Church News Nigeria","Mosque News Nigeria","Pastor News Nigeria","Imam News Nigeria",
            "Crusade Nigeria","Prayer in Nigeria","Pilgrimage Nigeria","Eid Celebration Nigeria","Christmas Celebration Nigeria"
        ),
        'Security' => array(
            "Security News Nigeria","Terrorism in Nigeria","Boko Haram News","Bandits in Nigeria","Kidnapping in Nigeria",
            "Nigerian Army News","Police News Nigeria","Insurgency in Nigeria","Counter Terrorism Nigeria","Cybercrime Nigeria",
            "EFCC News Today","Piracy in Nigeria","Oil Theft Nigeria","Security Summit Nigeria","Community Policing Nigeria"
        ),
        'Police' => array(
            "Nigeria Police News","NPF Nigeria","Police Brutality Nigeria","SARS Nigeria","Arrest News Nigeria",
            "IGP Nigeria","Police Reform Nigeria","Crime Prevention Nigeria","Criminal Investigation Nigeria","Police Recruitment Nigeria",
            "Police Promotion Nigeria","Detention News Nigeria","Tactical Squad Nigeria","Police PRO Nigeria","Interpol Nigeria"
        ),
        'Sports' => array(
            "Nigeria Sports News","Nigeria Football News","Super Eagles News","NPFL Nigeria","Nigerian Sports Updates","Lagos Football",
            "Nigerian Athletes","Nigerian Premier League","AFCON 2025 Qualifiers","World Cup Qualifiers Nigeria","Victor Osimhen News",
            "Victor Boniface News","Anthony Joshua Fight","Nigerian Basketball News","D'Tigers Nigeria","NFF Nigeria",
            "Transfer News Nigeria","Champions League Nigeria","Europa League Nigeria"
        ),
        'Technology' => array(
            "Nigerian Tech News","Nigeria Startups","Fintech Nigeria","Tech Startups Nigeria","Mobile Apps Nigeria","AI in Nigeria",
            "ICT in Nigeria","Broadband Internet Nigeria","Data Plans Nigeria","Tech Innovation Nigeria","Startup Funding Nigeria",
            "Tech Hubs Nigeria","E-commerce Nigeria","Cybersecurity Nigeria","Digital Economy Nigeria"
        ),
        'World Stage' => array(
            "World News Today","International News Nigeria","US News Today","UK News Today","China News Nigeria",
            "Russia News Today","ECOWAS News Nigeria","AU News Nigeria","UN News Nigeria","Niger Coup Update",
            "Ghana News Nigeria","South Africa News Today","Global Economy News","Climate Change News","Nigerians in Diaspora"
        ),
        'Lifestyle' => array(
            "Nigeria Lifestyle News","Fashion Nigeria","Food and Recipes Nigeria","Beauty Trends Nigeria","Travel Nigeria",
            "Tourism in Nigeria","Nigerian Weddings","Family Life Nigeria","Relationship Tips Nigeria","Culture Nigeria",
            "Lagos Lifestyle","Abuja Lifestyle","Youth Culture Nigeria","Nigerian Traditions","Lifestyle Bloggers Nigeria"
        ),
        'Economy' => array(
            "Nigeria Economy News","Inflation in Nigeria","Exchange Rate Nigeria","CBN Monetary Policy","Nigerian Debt News",
            "National Budget Nigeria","Economic Growth Nigeria","Unemployment in Nigeria","Poverty in Nigeria","Nigeria GDP",
            "Federal Revenue Nigeria","Fiscal Policy Nigeria","Global Oil Prices Nigeria","Nigeria Economic Summit","Development Plan Nigeria"
        )
    );

    // Pick 3 tags from different categories for better distribution
    $category_names = array_keys($tag_categories);
    $selected_categories = array_rand($category_names, min(3, count($category_names)));

    // Ensure we have an array even if only one category is selected
    if (!is_array($selected_categories)) {
        $selected_categories = array($selected_categories);
    }

    $tags_to_add = array();
    foreach ($selected_categories as $category_index) {
        $category_name = $category_names[$category_index];
        $category_tags = $tag_categories[$category_name];

        // Pick one random tag from this category
        $random_tag = $category_tags[array_rand($category_tags)];
        $tags_to_add[] = $random_tag;
    }

    // If we need more tags (shouldn't happen with 16 categories), fill from random categories
    while (count($tags_to_add) < 3 && count($tags_to_add) < count($category_names)) {
        $remaining_categories = array_diff($category_names, array_keys(array_flip($tags_to_add)));
        if (empty($remaining_categories)) break;

        $random_category = $remaining_categories[array_rand($remaining_categories)];
        $random_tag = $tag_categories[$random_category][array_rand($tag_categories[$random_category])];
        $tags_to_add[] = $random_tag;
    }

    wp_set_post_tags($post_ID, $tags_to_add, true);

    // Mark that we've added tags to this post
    update_post_meta($post_ID, '_adt_tags_added', true);

    return true;
}

add_action('publish_post', 'adt_add_default_tags_to_post');

// Add admin menu
add_action('admin_menu', 'adt_add_admin_menu');

function adt_add_admin_menu() {
    add_management_page(
        'Auto Default Tags',
        'Auto Default Tags',
        'manage_options',
        'auto-default-tags',
        'adt_admin_page'
    );
}

// Admin page content
function adt_admin_page() {
    // Handle form submission
    if (isset($_POST['add_tags_to_posts']) && wp_verify_nonce($_POST['adt_nonce'], 'adt_add_tags')) {
        $selected_posts = isset($_POST['selected_posts']) ? $_POST['selected_posts'] : array();
        $added_count = 0;

        foreach ($selected_posts as $post_id) {
            if (adt_add_tags_to_existing_post($post_id)) {
                $added_count++;
            }
        }

        echo '<div class="notice notice-success"><p>Tags added to ' . $added_count . ' posts successfully!</p></div>';
    }

    // Get posts without tags from this plugin
    $posts = get_posts(array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'numberposts' => 200,
        'meta_query' => array(
            array(
                'key' => '_adt_tags_added',
                'compare' => 'NOT EXISTS'
            )
        )
    ));

    ?>
    <div class="wrap">
        <h1>Auto Default Tags - Add to Existing Posts</h1>
        <p>Select posts to add 3 random Nigerian news tags. Only posts without existing auto-tags are shown.</p>

        <?php if (empty($posts)): ?>
            <div class="notice notice-info">
                <p>No posts found that need tags. All your posts already have auto-tags applied!</p>
            </div>
        <?php else: ?>
            <form method="post" action="">
                <?php wp_nonce_field('adt_add_tags', 'adt_nonce'); ?>

                <div style="margin: 20px 0;">
                    <button type="button" id="select-all" class="button">Select All</button>
                    <button type="button" id="deselect-all" class="button">Deselect All</button>
                </div>

                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th style="width: 50px;"><input type="checkbox" id="select-all-checkbox"></th>
                            <th>Post Title</th>
                            <th>Date Published</th>
                            <th>Current Tags</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($posts as $post): ?>
                            <tr>
                                <td><input type="checkbox" name="selected_posts[]" value="<?php echo $post->ID; ?>"></td>
                                <td>
                                    <strong><?php echo esc_html($post->post_title); ?></strong>
                                    <br><small>ID: <?php echo $post->ID; ?></small>
                                </td>
                                <td><?php echo get_the_date('Y-m-d H:i', $post->ID); ?></td>
                                <td>
                                    <?php
                                    $tags = get_the_tags($post->ID);
                                    if ($tags) {
                                        $tag_names = array_map(function($tag) { return $tag->name; }, $tags);
                                        echo esc_html(implode(', ', $tag_names));
                                    } else {
                                        echo '<em>No tags</em>';
                                    }
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <div style="margin: 20px 0;">
                    <input type="submit" name="add_tags_to_posts" class="button button-primary" value="Add Tags to Selected Posts">
                </div>
            </form>

            <script>
            document.getElementById('select-all').addEventListener('click', function() {
                var checkboxes = document.querySelectorAll('input[name="selected_posts[]"]');
                var selectAllCheckbox = document.getElementById('select-all-checkbox');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = true;
                });
                selectAllCheckbox.checked = true;
            });

            document.getElementById('deselect-all').addEventListener('click', function() {
                var checkboxes = document.querySelectorAll('input[name="selected_posts[]"]');
                var selectAllCheckbox = document.getElementById('select-all-checkbox');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = false;
                });
                selectAllCheckbox.checked = false;
            });

            document.getElementById('select-all-checkbox').addEventListener('change', function() {
                var checkboxes = document.querySelectorAll('input[name="selected_posts[]"]');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = this.checked;
                }, this);
            });
            </script>
        <?php endif; ?>
    </div>
    <?php
}